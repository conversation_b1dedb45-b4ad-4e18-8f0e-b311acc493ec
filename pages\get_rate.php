<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();
header('Content-Type: application/json');

// Include the database connection file
if (!@include '../db_connect.php') {
    echo json_encode(['status' => 'error', 'message' => 'Database connection file not found']);
    error_log("Database connection file not found.");
    exit;
}

if (!isset($conn)) {
    echo json_encode(['status' => 'error', 'message' => 'Database connection failed']);
    error_log("Database connection failed.");
    exit;
}

if (isset($_GET['customer'], $_GET['mode'], $_GET['zone'], $_GET['weight'])) {
    $customer = $_GET['customer'];
    $mode = $_GET['mode'];
    $zone = $_GET['zone'];
    $weight = floatval($_GET['weight']);
    $username = $_SESSION['username'];

    // Debug: Log received parameters
    error_log("Received parameters - Customer: $customer, Mode: $mode, Zone: $zone, Weight: $weight, Username: $username");

    $query = "SELECT up_to_0250, up_to_0500, addl_500gm, above_3kg, fsc_percent FROM rate_master 
              WHERE short_name = ? 
              AND mode_of_tsp = ? 
              AND zone = ? 
              AND username = ?";
    $stmt = $conn->prepare($query);

    if (!$stmt) {
        echo json_encode(['status' => 'error', 'message' => 'Failed to prepare statement: ' . $conn->error]);
        error_log("Failed to prepare statement: " . $conn->error);
        exit;
    }

    $stmt->bind_param("ssss", $customer, $mode, $zone, $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        $amount = 0;

        if ($mode === 'Premium') {
            // For Premium mode: flat per kg rate in rounds of 1kg
            $rounded_weight = ceil($weight);
            $amount = $rounded_weight * $row['above_3kg'];
        } else {
            // For Express and other modes: use existing weight slab logic
            if ($weight <= 0.250) {
                $amount = $row['up_to_0250'];
            } elseif ($weight <= 0.500) {
                $amount = $row['up_to_0500'];
            } elseif ($weight <= 3.000) {
                $amount = $row['up_to_0500'] + ceil(($weight - 0.500) / 0.500) * $row['addl_500gm'];
            } else {
                $amount = $row['up_to_0500'] + ceil((3.000 - 0.500) / 0.500) * $row['addl_500gm'] + ceil(($weight - 3.000)) * $row['above_3kg'];
            }
        }

        // FSC Calculation
        $fsc_percent = $row['fsc_percent'];
        $fsc = ($amount * $fsc_percent) / 100;
        $amount += $fsc;

        // Debug: Log calculated amount
        error_log("Calculated amount: $amount");

        echo json_encode(['status' => 'success', 'amount' => $amount]);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Rate Not Found']);
        error_log("Rate not found for the given parameters.");
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'Invalid Request']);
    error_log("Invalid request: Missing parameters.");
}
?>