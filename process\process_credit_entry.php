<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/process_credit_entry.log');

session_start();
include '../db_connect.php'; // Database connection

// Add debugging output to browser console via JavaScript
function console_log($message, $data = null) {
    $json_data = $data ? json_encode($data) : 'null';
    echo "<script>console.log('🐛 TRACKON CREDIT PHP DEBUG: " . addslashes($message) . "', " . $json_data . ");</script>";
}

error_log("=== Starting process_credit_entry.php execution ===");
error_log("Session username: " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set'));

if (!isset($_SESSION['username'])) {
    error_log("Error: User not logged in");
    die("<script>alert('Error: You must be logged in.'); window.location.href='login.php';</script>");
}

$username = $_SESSION['username']; // Get the logged-in user

// Debug: Log initial processing start
echo "<script>
console.log('🚀 TRACKON PROCESS_CREDIT_ENTRY.PHP START');
console.log('👤 Session Username:', '" . addslashes($username) . "');
console.log('📥 POST Data Received:', " . json_encode($_POST) . ");
console.log('🔍 Confirm Duplicate Flag:', '" . (isset($_POST['confirm_duplicate']) ? $_POST['confirm_duplicate'] : 'NOT SET') . "');
</script>";
$customer = $_POST['customer'];
$to_party = $_POST['to_party'];
$docket_no = $_POST['docket_no'];
$docket_date = $_POST['docket_date'];
$pincode = $_POST['pincode'];
$destination = $_POST['destination'];
$weight = floatval($_POST['weight']); // Convert to float
$mode_of_tsp = $_POST['mode_of_tsp'];
$waybill_value = floatval($_POST['waybill_value']); // Convert to float
$remarks = $_POST['remarks'];

error_log("Processing request for username: " . $username);

// ========================= SINGLE CREDIT ENTRY ========================= //
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['customer'])) {
    error_log("Processing single credit entry");
    error_log("POST data received: " . print_r($_POST, true));

    error_log("Form data extracted:");
    error_log("Customer: " . $customer);
    error_log("Docket No: " . $docket_no);
    error_log("Weight: " . $weight);
    error_log("Mode of TSP: " . $mode_of_tsp);

    // Fetch customer percentage rates
    error_log("Fetching customer rates for: " . $customer);
    $customer_rates_stmt = $conn->prepare("SELECT waybill_percent, owner_risk, carrier_risk FROM customers WHERE short_name = ? AND username = ?");
    if (!$customer_rates_stmt) {
        error_log("Error preparing customer rates statement: " . $conn->error);
    }

    $customer_rates_stmt->bind_param("ss", $customer, $username);
    $customer_rates_stmt->execute();
    $customer_rates_result = $customer_rates_stmt->get_result();
    $customer_rates = $customer_rates_result->fetch_assoc();
    $customer_rates_stmt->close();

    error_log("Customer rates fetched: " . print_r($customer_rates, true));

    // Calculate percentage values
    $waybill_value = floatval($waybill_value);
    $waybill_percent = floatval($customer_rates['waybill_percent']);
    $owner_risk = floatval($customer_rates['owner_risk']);
    $carrier_risk = floatval($customer_rates['carrier_risk']);

    $waybill_percent_value = ($waybill_value * $waybill_percent) / 100;
    $owner_risk_value = ($waybill_value * $owner_risk) / 100;
    $carrier_risk_value = ($waybill_value * $carrier_risk) / 100;

    error_log("Calculated values:");
    error_log("Waybill percent value: " . $waybill_percent_value);
    error_log("Owner risk value: " . $owner_risk_value);
    error_log("Carrier risk value: " . $carrier_risk_value);

    // Validate docket number exists in cn_entries and matches with selected customer
    error_log("Starting docket validation in process_credit_entry.php");
    error_log("Validating - Docket: $docket_no, Customer: $customer");

    // Skip CN number validation check
    error_log("Skipping CN number validation as per request");

    // Check for Duplicate Docket with Confirmation
    error_log("Checking for duplicate docket: " . $docket_no);
    $check_stmt = $conn->prepare("SELECT docket_no, docket_date, created_at, entry_type FROM transactions WHERE docket_no = ? AND username = ? ORDER BY created_at DESC LIMIT 1");
    if (!$check_stmt) {
        error_log("Error preparing duplicate check statement: " . $conn->error);
    }

    $check_stmt->bind_param("ss", $docket_no, $username);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $existing_entry = $check_result->fetch_assoc();
    $check_stmt->close();

    // Check if user has confirmed duplicate entry
    $confirm_duplicate = isset($_POST['confirm_duplicate']) && $_POST['confirm_duplicate'] == '1';

    // Format the existing entry date information
    $existing_date_info = '';
    $existing_entry_type = '';
    if ($existing_entry) {
        $existing_entry_type = ucfirst($existing_entry['entry_type']);
        $docket_date_existing = date('d-M-Y', strtotime($existing_entry['docket_date']));
        $created_date = date('d-M-Y H:i', strtotime($existing_entry['created_at']));
        $existing_date_info = "Originally used on: $docket_date_existing (Entry created: $created_date)";
    }

    echo "<script>
    console.log('🔍 TRACKON CREDIT DUPLICATE CHECK RESULTS:');
    console.log('  Duplicate entry found:', " . ($existing_entry ? 'true' : 'false') . ");
    console.log('  Existing entry type:', '" . addslashes($existing_entry_type) . "');
    console.log('  Existing docket date:', '" . addslashes($existing_entry ? $existing_entry['docket_date'] : 'N/A') . "');
    console.log('  Existing created date:', '" . addslashes($existing_entry ? $existing_entry['created_at'] : 'N/A') . "');
    console.log('  Confirm duplicate flag:', " . ($confirm_duplicate ? 'true' : 'false') . ");
    console.log('  Will show confirmation:', " . (($existing_entry && !$confirm_duplicate) ? 'true' : 'false') . ");
    </script>";

    if ($existing_entry && !$confirm_duplicate) {
        // Duplicate exists and user hasn't confirmed - ask for confirmation
        echo "<script>console.log('⚠️ SHOWING TRACKON CREDIT DUPLICATE CONFIRMATION DIALOG');</script>";
        error_log("Duplicate docket found: " . $docket_no . " - showing confirmation dialog");
        $conn->close();

        // Create form data to resubmit with confirmation
        $form_data = '';
        foreach ($_POST as $key => $value) {
            if ($key !== 'confirm_duplicate') {
                $form_data .= '<input type="hidden" name="' . htmlspecialchars($key) . '" value="' . htmlspecialchars($value) . '">';
            }
        }

        echo '
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Trackon Credit - Duplicate Confirmation</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                    padding: 20px;
                }

                .modal {
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                    max-width: 500px;
                    width: 100%;
                    animation: modalSlideIn 0.3s ease-out;
                    overflow: hidden;
                }

                @keyframes modalSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-50px) scale(0.9);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }

                .modal-header {
                    background: linear-gradient(135deg, #00b894, #55efc4);
                    color: white;
                    padding: 24px;
                    text-align: center;
                }

                .modal-icon {
                    font-size: 48px;
                    margin-bottom: 12px;
                    display: block;
                }

                .modal-title {
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }

                .modal-subtitle {
                    font-size: 16px;
                    opacity: 0.9;
                }

                .modal-body {
                    padding: 32px 24px;
                    text-align: center;
                }

                .warning-message {
                    font-size: 18px;
                    color: #2c3e50;
                    line-height: 1.6;
                    margin-bottom: 8px;
                }

                .docket-highlight {
                    font-weight: 700;
                    color: #00b894;
                    background: #d1f2eb;
                    padding: 4px 8px;
                    border-radius: 6px;
                    display: inline-block;
                    margin: 0 4px;
                }

                .sub-message {
                    font-size: 16px;
                    color: #7f8c8d;
                    margin-top: 16px;
                }

                .trackon-credit-badge {
                    display: inline-block;
                    background: linear-gradient(135deg, #00b894, #55efc4);
                    color: white;
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 600;
                    margin-bottom: 16px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .existing-entry-info {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 20px 0;
                    text-align: left;
                }

                .entry-type-badge {
                    display: inline-block;
                    background: linear-gradient(135deg, #00b894, #55efc4);
                    color: white;
                    padding: 4px 10px;
                    border-radius: 12px;
                    font-size: 11px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 12px;
                }

                .date-info {
                    font-size: 14px;
                    color: #495057;
                    line-height: 1.6;
                    margin: 0;
                }

                .date-info strong {
                    color: #343a40;
                    font-weight: 600;
                }

                .modal-footer {
                    padding: 24px;
                    background: #f8f9fa;
                    display: flex;
                    gap: 12px;
                    justify-content: center;
                }

                .btn {
                    padding: 12px 32px;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    min-width: 120px;
                }

                .btn-confirm {
                    background: linear-gradient(135deg, #00b894, #55efc4);
                    color: white;
                }

                .btn-confirm:hover {
                    background: linear-gradient(135deg, #00a085, #4dd0a7);
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
                }

                .btn-cancel {
                    background: #ddd;
                    color: #666;
                }

                .btn-cancel:hover {
                    background: #ccc;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                }

                .loading {
                    display: none;
                    text-align: center;
                    padding: 20px;
                }

                .spinner {
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #00b894;
                    border-radius: 50%;
                    width: 30px;
                    height: 30px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 12px;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        </head>
        <body>
            <div class="modal">
                <div class="modal-header">
                    <span class="modal-icon">🚛💳</span>
                    <h2 class="modal-title">Trackon Credit Duplicate</h2>
                    <p class="modal-subtitle">This docket number already exists</p>
                </div>

                <div class="modal-body">
                    <div class="trackon-credit-badge">Trackon Credit Entry</div>
                    <p class="warning-message">
                        Docket No. <span class="docket-highlight">' . htmlspecialchars($docket_no) . '</span> already exists for this user.
                    </p>
                    <div class="existing-entry-info">
                        <div class="entry-type-badge">' . htmlspecialchars($existing_entry_type) . ' Entry</div>
                        <p class="date-info">
                            <strong>📅 Docket Date:</strong> ' . htmlspecialchars($docket_date_existing) . '<br>
                            <strong>🕒 Entry Created:</strong> ' . htmlspecialchars($created_date) . '
                        </p>
                    </div>
                    <p class="sub-message">
                        Do you want to add this duplicate credit entry anyway?
                    </p>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-cancel" onclick="cancelDuplicate()">
                        ❌ Cancel
                    </button>
                    <button type="button" class="btn btn-confirm" onclick="confirmDuplicate()">
                        ✅ Yes, Add Duplicate
                    </button>
                </div>

                <div class="loading" id="loadingDiv">
                    <div class="spinner"></div>
                    <p>Processing credit entry...</p>
                </div>
            </div>

            <form id="confirmForm" method="POST" action="process_credit_entry.php" style="display:none;">
                ' . $form_data . '
                <input type="hidden" name="confirm_duplicate" value="1">
            </form>

            <script>
                console.log("🔍 TRACKON CREDIT MODAL DUPLICATE CONFIRMATION DEBUG START");
                console.log("📋 Docket Number:", "' . htmlspecialchars($docket_no) . '");
                console.log("👤 Username:", "' . htmlspecialchars($username) . '");

                function confirmDuplicate() {
                    console.log("✅ User confirmed duplicate credit entry");

                    const confirmForm = document.getElementById("confirmForm");
                    const loadingDiv = document.getElementById("loadingDiv");
                    const modalFooter = document.querySelector(".modal-footer");
                    const modalBody = document.querySelector(".modal-body");

                    if (!confirmForm) {
                        console.error("❌ ERROR: confirmForm element not found!");
                        alert("Error: Form not found. Please try again.");
                        window.history.back();
                        return;
                    }

                    // Show loading state
                    modalFooter.style.display = "none";
                    modalBody.style.display = "none";
                    loadingDiv.style.display = "block";

                    console.log("📤 Submitting trackon credit form with duplicate confirmation");

                    try {
                        confirmForm.submit();
                        console.log("📨 Trackon credit form submitted successfully");
                    } catch (error) {
                        console.error("❌ Form submission error:", error);
                        alert("Error submitting form: " + error.message);
                        // Restore modal state on error
                        modalFooter.style.display = "flex";
                        modalBody.style.display = "block";
                        loadingDiv.style.display = "none";
                    }
                }

                function cancelDuplicate() {
                    console.log("❌ User cancelled duplicate credit entry");
                    window.history.back();
                }

                // Auto-focus on confirm button for better UX
                document.addEventListener("DOMContentLoaded", function() {
                    console.log("🚀 Trackon credit modal loaded and ready");
                    document.querySelector(".btn-confirm").focus();
                });

                // Allow Enter key to confirm, Escape to cancel
                document.addEventListener("keydown", function(e) {
                    if (e.key === "Enter") {
                        confirmDuplicate();
                    } else if (e.key === "Escape") {
                        cancelDuplicate();
                    }
                });

                console.log("🔍 TRACKON CREDIT MODAL DUPLICATE CONFIRMATION DEBUG END");
            </script>
        </body>
        </html>
        ';
        exit;
    }

    // Fetch Zone from Pincode
    error_log("Fetching zone for pincode: " . $pincode);
    $zone = '';
    $ts_zone = '';
    $pr_zone = '';
    $zone_query = "SELECT zone, ts_zone, pr_zone FROM pincode_data WHERE pincode=?";
    $zone_stmt = $conn->prepare($zone_query);
    if (!$zone_stmt) {
        error_log("Error preparing zone statement: " . $conn->error);
    }

    $zone_stmt->bind_param("s", $pincode);
    $zone_stmt->execute();
    $zone_result = $zone_stmt->get_result();
    if ($zone_row = $zone_result->fetch_assoc()) {
        $zone = $zone_row['zone'];
        $ts_zone = $zone_row['ts_zone'];
        $pr_zone = $zone_row['pr_zone'];
        error_log("Zone found: " . $zone . ", TS Zone: " . $ts_zone . ", PR Zone: " . $pr_zone);
    } else {
        error_log("No zone found for pincode: " . $pincode);
        die("<script>alert('Error: Zone not found for pincode!'); window.history.back();</script>");
    }
    $zone_stmt->close();

    // Calculate old rate for amount
    error_log("Calculating old rate for amount");
    $amount = 0;

    // Fetch rates from rate_master for amount calculation
    $rate_mode = $mode_of_tsp === 'Air Cargo' ? 'Express' : $mode_of_tsp;
    $rate_query = "";
    if ($mode_of_tsp === 'Premium') {
        $rate_query = "SELECT * FROM rate_master WHERE short_name=? AND mode_of_tsp=? AND UPPER(pr_zone)=UPPER(?)";
        error_log("Using Premium mode with pr_zone: " . $pr_zone);
    } else {
        $rate_query = "SELECT * FROM rate_master WHERE short_name=? AND mode_of_tsp=? AND UPPER(zone)=UPPER(?)";
        error_log("Using regular mode with zone: " . $zone);
    }

    $rate_stmt = $conn->prepare($rate_query);
    if (!$rate_stmt) {
        error_log("Error preparing rate statement: " . $conn->error);
        die("<script>alert('Error: Database error occurred!'); window.history.back();</script>");
    }

    // Bind parameters based on mode
    if ($mode_of_tsp === 'Premium') {
        $rate_stmt->bind_param("sss", $customer, $rate_mode, $pr_zone);
    } else {
        $rate_stmt->bind_param("sss", $customer, $rate_mode, $zone);
    }

    $rate_stmt->execute();
    $rate_result = $rate_stmt->get_result();

    if ($rate_row = $rate_result->fetch_assoc()) {
        error_log("Rate data found for amount calculation: " . print_r($rate_row, true));
        if ($rate_mode === 'Surface') {
            // For Surface mode
            $min_weight = floatval($rate_row['above_3kg']); // minimum weight
            $actual_weight = max($weight, $min_weight);
            $rounded_weight = ceil($actual_weight);

            // Select rate based on weight slabs for Surface mode
            if ($rounded_weight <= 10) {
                $rate_used = floatval($rate_row['up_to_0250']); // 1-10 kg rate
                error_log("Surface mode - Using up_to_0250 rate: " . $rate_used);
            } elseif ($rounded_weight <= 20) {
                $rate_used = floatval($rate_row['up_to_0500']); // 11-20 kg rate
                error_log("Surface mode - Using up_to_0500 rate: " . $rate_used);
            } else {
                $rate_used = floatval($rate_row['addl_500gm']); // Above 20 kg rate
                error_log("Surface mode - Using addl_500gm rate: " . $rate_used);
            }

            $amount = $rounded_weight * $rate_used;
            error_log("Surface mode - Final amount: " . $amount);
        } else if ($rate_mode === 'Premium') {
            // For Premium mode: flat per kg rate in rounds of 1kg
            $rounded_weight = ceil($weight);
            $amount = $rounded_weight * floatval($rate_row['above_3kg']);
            error_log("Premium mode - Using flat per kg rate: " . $amount . " (weight: " . $weight . " -> rounded: " . $rounded_weight . ")");
        } else {
            // For Express mode
            if ($weight <= 0.250) {
                // First 250 grams, no rounding
                $amount = floatval($rate_row['up_to_0250']);
                error_log("Express mode - Using up_to_0250 rate: " . $amount);
            } else {
                // Round weight only if above 250 grams
                $weight = ceil($weight * 2) / 2;

                if ($weight <= 0.500) {
                    $amount = floatval($rate_row['up_to_0500']);
                    error_log("Express mode - Using up_to_0500 rate: " . $amount);
                } elseif ($weight <= 3) {
                    $amount = floatval($rate_row['up_to_0500']) + (($weight - 0.500) / 0.500) * floatval($rate_row['addl_500gm']);
                    error_log("Express mode - Calculated amount with addl_500gm: " . $amount);
                } else {
                    // Above 3 kg calculation
                    $amount = $weight * floatval($rate_row['above_3kg']);
                    error_log("Express mode - Using above_3kg rate: " . $amount);
                }
            }
        }
    } else {
        error_log("No rate found for amount calculation - mode: " . $rate_mode . ", zone: " . $zone);
        die("<script>alert('Error: Rate not found for given parameters! Please check if rates are configured for " . $rate_mode . " mode in the selected zone (" . $zone . ") for customer " . $customer . ".'); window.history.back();</script>");
    }

    // Calculate entry_ts for all modes including Premium
    error_log("Calculating new rate for entry_ts");
    $entry_ts = 0;

    // Fetch rates from ts_rate_master
    $ts_rate_query = "SELECT * FROM ts_rate_master WHERE username=? AND mode_of_tsp=? AND UPPER(zone)=UPPER(?)";
    $ts_rate_stmt = $conn->prepare($ts_rate_query);
    if (!$ts_rate_stmt) {
        error_log("Error preparing ts_rate statement: " . $conn->error);
        die("<script>alert('Error: Database error occurred!'); window.history.back();</script>");
    }

    $ts_rate_stmt->bind_param("sss", $username, $mode_of_tsp, $ts_zone);
    error_log("Using TS Zone for " . $mode_of_tsp . " mode: " . $ts_zone);

    $ts_rate_stmt->execute();
    $ts_rate_result = $ts_rate_stmt->get_result();

    if ($ts_rate_row = $ts_rate_result->fetch_assoc()) {
        error_log("TS Rate data found: " . print_r($ts_rate_row, true));
        $weight = floatval($weight);

        // Calculate rate based on weight slabs
        if ($mode_of_tsp === 'Surface') {
            // Existing Surface mode calculation
            // Apply minimum weight check first
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }

            // Round weight to nearest 1kg for Surface mode
            $rounded_weight = ceil($weight);
            error_log("Surface mode - Rounded weight: " . $rounded_weight);

            // Calculate rate based on weight slabs for Surface mode
            if ($rounded_weight <= 5.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_5kg']);
                error_log("Surface mode - Using up_to_5kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 10.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_10kg']);
                error_log("Surface mode - Using up_to_10kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 25.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_25kg']);
                error_log("Surface mode - Using up_to_25kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 50.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_50kg']);
                error_log("Surface mode - Using up_to_50kg rate: " . $entry_ts);
            } else {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                error_log("Surface mode - Using above_50kg rate: " . $entry_ts);
            }
        } elseif ($mode_of_tsp === 'Air Cargo') {
            // Apply minimum weight check first
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }

            // Round weight to nearest 1kg for Air Cargo mode (same as Surface)
            $rounded_weight = ceil($weight);
            error_log("Air Cargo mode - Rounded weight: " . $rounded_weight);

            // Calculate rate based on weight slabs for Air Cargo mode (same as Surface)
            if ($rounded_weight <= 5.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_5kg']);
                error_log("Air Cargo mode - Using up_to_5kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 10.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_10kg']);
                error_log("Air Cargo mode - Using up_to_10kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 25.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_25kg']);
                error_log("Air Cargo mode - Using up_to_25kg rate: " . $entry_ts);
            } elseif ($rounded_weight <= 50.000) {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_50kg']);
                error_log("Air Cargo mode - Using up_to_50kg rate: " . $entry_ts);
            } else {
                $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                error_log("Air Cargo mode - Using above_50kg rate: " . $entry_ts);
            }
        } elseif ($mode_of_tsp === 'Premium') {
            // Apply minimum weight check first
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }

            // Calculate rate based on flat per kg for Premium mode
            $rounded_weight = ceil($weight);
            $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
            error_log("Premium mode - Using flat per kg rate: " . $entry_ts . " (weight: " . $weight . " -> rounded: " . $rounded_weight . ")");
        } else {
            // Express rate calculation
            if ($weight <= 0.100) {
                $entry_ts = floatval($ts_rate_row['up_to_0100']);
                error_log("Weight <= 0.100kg, rate: " . $entry_ts);
            } elseif ($weight <= 0.250) {
                $entry_ts = floatval($ts_rate_row['up_to_0250']);
                error_log("Weight <= 0.250kg, rate: " . $entry_ts);
            } elseif ($weight <= 0.500) {
                $entry_ts = floatval($ts_rate_row['up_to_0500']);
                error_log("Weight <= 0.500kg, rate: " . $entry_ts);
            } elseif ($weight <= 3.000) {
                $base_rate = floatval($ts_rate_row['up_to_0500']);
                $additional_weight = ceil(($weight - 0.500) * 2);
                $entry_ts = $base_rate + ($additional_weight * floatval($ts_rate_row['addl_500gm']));
                error_log("Weight <= 3.000kg, calculated rate: " . $entry_ts);
            } elseif ($weight <= 5.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_5kg']);
                error_log("Weight <= 5.000kg, rate: " . $entry_ts);
            } elseif ($weight <= 10.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_10kg']);
                error_log("Weight <= 10.000kg, rate: " . $entry_ts);
            } elseif ($weight <= 25.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_25kg']);
                error_log("Weight <= 25.000kg, rate: " . $entry_ts);
            } elseif ($weight <= 50.000) {
                $entry_ts = $weight * floatval($ts_rate_row['up_to_50kg']);
                error_log("Weight <= 50.000kg, rate: " . $entry_ts);
            } else {
                $entry_ts = $weight * floatval($ts_rate_row['above_50kg']);
                error_log("Weight > 50.000kg, rate: " . $entry_ts);
            }

            // Apply minimum weight check for Express mode
            $min_weight = floatval($ts_rate_row['min_weight']);
            if ($weight < $min_weight) {
                $weight = $min_weight;
                error_log("Applied minimum weight: " . $min_weight);
            }
        }
    } else {
        error_log("No TS rate found for mode: " . $mode_of_tsp . ", zone: " . $ts_zone);
        die("<script>alert('Error: Rate not found in ts_rate_master for given parameters!'); window.history.back();</script>");
    }
    $ts_rate_stmt->close();

    // Insert into Transactions Table
    error_log("Preparing to insert into transactions table");

    echo "<script>
    console.log('✅ TRACKON PROCEEDING TO DATABASE INSERTION');
    console.log('📋 Final Data to Insert:');
    console.log('  docket_no:', '" . addslashes($docket_no) . "');
    console.log('  customer:', '" . addslashes($customer) . "');
    console.log('  to_party:', '" . addslashes($to_party) . "');
    console.log('  docket_date:', '" . addslashes($docket_date) . "');
    console.log('  pincode:', '" . addslashes($pincode) . "');
    console.log('  destination:', '" . addslashes($destination) . "');
    console.log('  weight:', '" . addslashes($weight) . "');
    console.log('  mode_of_tsp:', '" . addslashes($mode_of_tsp) . "');
    console.log('  waybill_value:', '" . addslashes($waybill_value) . "');
    console.log('  amount:', '" . addslashes($amount) . "');
    console.log('  entry_ts:', '" . addslashes($entry_ts) . "');
    console.log('  username:', '" . addslashes($username) . "');
    </script>";

    $stmt = $conn->prepare("
        INSERT INTO transactions
        (id, s_name, entry_type, customer, to_party, docket_no, docket_date, pincode, destination, weight, mode_of_tsp, s_pro, waybill_value, remarks, amount, entry_ts, waybill_percent, oda_chrg, owner_risk, carrier_risk, payment_status, payment_received_date, waybill, mobile1, mobile2, username, created_at)
        VALUES (NULL, '', 'credit', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Pending', NULL, NULL, NULL, NULL, ?, CURRENT_TIMESTAMP)
    ");

    // Add more detailed error checking
    if ($stmt === false) {
        echo "<script>console.error('❌ Prepare statement failed:', '" . addslashes($conn->error) . "');</script>";
        error_log("Error preparing insert statement: " . $conn->error);
        die("<script>alert('Database prepare error: " . addslashes($conn->error) . "'); window.history.back();</script>");
    }

    error_log("Binding parameters with amount: " . $amount . " and entry_ts: " . $entry_ts);

    $entry_type = 'credit';
    $oda_charges = $_POST['oda_charges'] ?? 0;
    $risk_type = $_POST['risk_charges'];
    $risk_value = $_POST['risk_charges_value'] ?? 0;
    $s_pro = $_POST['s_pro'] ?? '';

    // Set owner_risk and carrier_risk values based on risk type
    $owner_risk_value = ($risk_type === 'Owner Risk') ? $risk_value : 0;
    $carrier_risk_value = ($risk_type === 'Carrier Risk') ? $risk_value : 0;

    $stmt->bind_param("ssssssssssssssssss",
        $customer,
        $to_party,
        $docket_no,
        $docket_date,
        $pincode,
        $destination,
        $weight,
        $mode_of_tsp,
        $s_pro,
        $waybill_value,
        $remarks,
        $amount,
        $entry_ts,
        $customer_rates['waybill_percent'],
        $oda_charges,
        $owner_risk_value,
        $carrier_risk_value,
        $username
    );

    echo "<script>console.log('💾 Executing database insertion...');</script>";
    $result = $stmt->execute();

    if ($result) {
        echo "<script>console.log('✅ Database insertion successful! Inserted ID: " . $conn->insert_id . "');</script>";
        error_log("Transaction successfully inserted");
    } else {
        echo "<script>console.error('❌ Database insertion failed:', '" . addslashes($stmt->error) . "');</script>";
        echo "<script>console.error('❌ MySQL Error Code:', " . $stmt->errno . ");</script>";
        error_log("Error executing insert statement: " . $stmt->error);

        // Check if it's a duplicate key error (Error code 1062)
        if ($stmt->errno == 1062) {
            echo "<script>console.error('🚫 UNIQUE CONSTRAINT VIOLATION: The database has a UNIQUE constraint on (docket_no, username) that prevents duplicate entries.');</script>";
            die("<script>
                alert('❌ Database Constraint Error:\\n\\nThe database has a UNIQUE constraint that prevents duplicate docket numbers for the same user.\\n\\nTo allow duplicate entries, the database constraint needs to be removed.\\n\\nError: " . addslashes($stmt->error) . "');
                window.history.back();
            </script>");
        } else {
            die("<script>alert('Database insertion error: " . addslashes($stmt->error) . "'); window.history.back();</script>");
        }
    }
    $stmt->close();

    echo "<script>
    console.log('🎉 TRACKON PROCESS COMPLETED SUCCESSFULLY');
    alert('✅ Credit Entry successfully saved!');
    window.location.href = 'https://trackon.astradigitalsolutions.in/index.php?page=credit-entry';
    </script>";
}

// ========================= BULK EXCEL UPLOAD ========================= //
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    error_log("Processing bulk excel upload");
    require __DIR__ . '/../vendor/autoload.php';

    try {
        $file = $_FILES['excel_file']['tmp_name'];
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file);
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();

        $successCount = 0;
        $errorCount = 0;
        $errorMessages = [];
        $successMessages = [];

        error_log("Processing " . count($rows) . " rows from excel file");

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // Skip Header

            error_log("Processing row " . $index);
            list($entry_type, $customer, $docket_no, $docket_date, $pincode, $destination, $weight, $mode_of_tsp, $waybill_value, $remarks, $file_username) = $row;

            if ($file_username !== $username) {
                $errorMessages[] = "Row $index: Username mismatch.";
                $errorCount++;
                continue;
            }

            // Validate CN number and customer allocation
            $validate_stmt = $conn->prepare("SELECT customer FROM cn_entries WHERE cn_number = ?");
            $validate_stmt->bind_param("s", $docket_no);
            $validate_stmt->execute();
            $validate_result = $validate_stmt->get_result();

            if ($validate_result->num_rows === 0) {
                $errorMessages[] = "Row $index: CN Number not found in database.";
                $errorCount++;
                $validate_stmt->close();
                continue;
            }

            $cn_data = $validate_result->fetch_assoc();
            error_log("Bulk Upload - Found CN data for $docket_no - Stored Customer: {$cn_data['customer']}, Input Customer: $customer");

            // If CN exists but has no customer allocated, allow entry
            if (!empty($cn_data['customer']) && $cn_data['customer'] !== $customer) {
                $errorMessages[] = "Row $index: CN Number is already allocated to another customer.";
                $errorCount++;
                $validate_stmt->close();
                continue;
            }
            $validate_stmt->close();

            // Check if docket_no is already used in transactions
            $trans_check_stmt = $conn->prepare("SELECT docket_no FROM transactions WHERE docket_no = ?");
            $trans_check_stmt->bind_param("s", $docket_no);
            $trans_check_stmt->execute();
            $trans_check_stmt->store_result();

            if ($trans_check_stmt->num_rows > 0) {
                $errorMessages[] = "Row $index: Docket number already used for another shipment.";
                $errorCount++;
                $trans_check_stmt->close();
                continue;
            }
            $trans_check_stmt->close();

            // Fetch customer percentage rates for bulk upload
            $customer_rates_query = "SELECT waybill_percent, owner_risk, carrier_risk FROM customers WHERE short_name='$customer' AND username='$username'";
            $customer_rates_result = mysqli_query($conn, $customer_rates_query);
            $customer_rates = mysqli_fetch_assoc($customer_rates_result);

            if (!$customer_rates) {
                $errorMessages[] = "Row $index: Customer rates not found.";
                $errorCount++;
                continue;
            }

            $date = DateTime::createFromFormat('d/m/Y', $docket_date);
            if ($date) {
                $docket_date = $date->format('Y-m-d');
            } else {
                $errorMessages[] = "Row $index: Invalid date format.";
                $errorCount++;
                continue;
            }

            // Fetch Zone
            $zone_query = "SELECT zone, ts_zone, pr_zone FROM pincode_data WHERE pincode='$pincode'";
            $zone_result = mysqli_query($conn, $zone_query);
            $zone_data = mysqli_fetch_assoc($zone_result);
            $zone = $zone_data['zone'];
            $ts_zone = $zone_data['ts_zone'];
            $pr_zone = $zone_data['pr_zone'];

            // Calculate old rate for amount
            $rate_mode = $mode_of_tsp === 'Air Cargo' ? 'Express' : $mode_of_tsp;
            $rate_query = "";
            if ($mode_of_tsp === 'Premium') {
                $rate_query = "SELECT * FROM rate_master WHERE short_name='$customer' AND mode_of_tsp='$rate_mode' AND UPPER(pr_zone)=UPPER('$pr_zone')";
                error_log("Bulk Upload - Using Premium mode with pr_zone: " . $pr_zone);
            } else {
                $rate_query = "SELECT * FROM rate_master WHERE short_name='$customer' AND mode_of_tsp='$rate_mode' AND UPPER(zone)=UPPER('$zone')";
                error_log("Bulk Upload - Using regular mode with zone: " . $zone);
            }
            $rate_result = mysqli_query($conn, $rate_query);
            $amount = 0;

            if ($rate_row = mysqli_fetch_assoc($rate_result)) {
                if ($rate_mode === 'Surface') {
                    // For Surface mode
                    $min_weight = floatval($rate_row['above_3kg']); // minimum weight
                    $actual_weight = max($weight, $min_weight);
                    $rounded_weight = ceil($actual_weight);

                    // Select rate based on weight slabs for Surface mode
                    if ($rounded_weight <= 10) {
                        $rate_used = floatval($rate_row['up_to_0250']); // 1-10 kg rate
                    } elseif ($rounded_weight <= 20) {
                        $rate_used = floatval($rate_row['up_to_0500']); // 11-20 kg rate
                    } else {
                        $rate_used = floatval($rate_row['addl_500gm']); // Above 20 kg rate
                    }

                    $amount = $rounded_weight * $rate_used;
                } else if ($rate_mode === 'Premium') {
                    // For Premium mode: flat per kg rate in rounds of 1kg
                    $rounded_weight = ceil($weight);
                    $amount = $rounded_weight * $rate_row['above_3kg'];
                } else {
                    // For Express mode
                    if ($weight <= 0.250) {
                        // First 250 grams, no rounding
                        $amount = $rate_row['up_to_0250'];
                    } else {
                        // Round weight only if above 250 grams
                        $weight = ceil($weight * 2) / 2;

                        if ($weight <= 0.500) {
                            $amount = $rate_row['up_to_0500'];
                        } elseif ($weight <= 3) {
                            $amount = $rate_row['up_to_0500'] + (($weight - 0.500) / 0.500) * $rate_row['addl_500gm'];
                        } else {
                            // Above 3 kg calculation
                            $amount = $weight * $rate_row['above_3kg'];
                        }
                    }
                }
            }

            // Calculate entry_ts for all modes including Premium
            error_log("Calculating new rate for entry_ts");
            $entry_ts = 0;

            // Fetch rates from ts_rate_master
            $ts_rate_query = "SELECT * FROM ts_rate_master WHERE username='$username' AND mode_of_tsp='$mode_of_tsp' AND UPPER(zone)=UPPER('$ts_zone')";
            $ts_rate_result = mysqli_query($conn, $ts_rate_query);

            if ($ts_rate_row = mysqli_fetch_assoc($ts_rate_result)) {
                $weight = floatval($weight);

                // Calculate rate based on weight slabs
                if ($mode_of_tsp === 'Surface') {
                    // Apply minimum weight check first
                    $min_weight = floatval($ts_rate_row['min_weight']);
                    if ($weight < $min_weight) {
                        $weight = $min_weight;
                        error_log("Applied minimum weight: " . $min_weight);
                    }

                    // Round weight to nearest 1kg for Surface mode
                    $rounded_weight = ceil($weight);

                    // Calculate rate based on weight slabs for Surface mode
                    if ($rounded_weight <= 5.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_5kg']);
                    } elseif ($rounded_weight <= 10.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_10kg']);
                    } elseif ($rounded_weight <= 25.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_25kg']);
                    } elseif ($rounded_weight <= 50.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_50kg']);
                    } else {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                    }
                } elseif ($mode_of_tsp === 'Air Cargo') {
                    // Apply minimum weight check first
                    $min_weight = floatval($ts_rate_row['min_weight']);
                    if ($weight < $min_weight) {
                        $weight = $min_weight;
                        error_log("Applied minimum weight: " . $min_weight);
                    }

                    // Round weight to nearest 1kg for Air Cargo mode (same as Surface)
                    $rounded_weight = ceil($weight);

                    // Calculate rate based on weight slabs for Air Cargo mode (same as Surface)
                    if ($rounded_weight <= 5.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_5kg']);
                    } elseif ($rounded_weight <= 10.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_10kg']);
                    } elseif ($rounded_weight <= 25.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_25kg']);
                    } elseif ($rounded_weight <= 50.000) {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['up_to_50kg']);
                    } else {
                        $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                    }
                } elseif ($mode_of_tsp === 'Premium') {
                    // Apply minimum weight check first
                    $min_weight = floatval($ts_rate_row['min_weight']);
                    if ($weight < $min_weight) {
                        $weight = $min_weight;
                        error_log("Applied minimum weight: " . $min_weight);
                    }

                    // Calculate rate based on flat per kg for Premium mode
                    $rounded_weight = ceil($weight);
                    $entry_ts = $rounded_weight * floatval($ts_rate_row['above_50kg']);
                    error_log("Premium mode - Using flat per kg rate: " . $entry_ts . " (weight: " . $weight . " -> rounded: " . $rounded_weight . ")");
                } else {
                    // Express rate calculation
                    if ($weight <= 0.100) {
                        $entry_ts = floatval($ts_rate_row['up_to_0100']);
                    } elseif ($weight <= 0.250) {
                        $entry_ts = floatval($ts_rate_row['up_to_0250']);
                    } elseif ($weight <= 0.500) {
                        $entry_ts = floatval($ts_rate_row['up_to_0500']);
                    } elseif ($weight <= 3.000) {
                        $base_rate = floatval($ts_rate_row['up_to_0500']);
                        $additional_weight = ceil(($weight - 0.500) * 2);
                        $entry_ts = $base_rate + ($additional_weight * floatval($ts_rate_row['addl_500gm']));
                        error_log("Weight <= 3.000kg, calculated rate: " . $entry_ts);
                    } elseif ($weight <= 5.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_5kg']);
                        error_log("Weight <= 5.000kg, rate: " . $entry_ts);
                    } elseif ($weight <= 10.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_10kg']);
                        error_log("Weight <= 10.000kg, rate: " . $entry_ts);
                    } elseif ($weight <= 25.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_25kg']);
                        error_log("Weight <= 25.000kg, rate: " . $entry_ts);
                    } elseif ($weight <= 50.000) {
                        $entry_ts = $weight * floatval($ts_rate_row['up_to_50kg']);
                        error_log("Weight <= 50.000kg, rate: " . $entry_ts);
                    } else {
                        $entry_ts = $weight * floatval($ts_rate_row['above_50kg']);
                        error_log("Weight > 50.000kg, rate: " . $entry_ts);
                    }

                    // Apply minimum weight check for Express mode
                    $min_weight = floatval($ts_rate_row['min_weight']);
                    if ($weight < $min_weight) {
                        $weight = $min_weight;
                        error_log("Applied minimum weight: " . $min_weight);
                    }
                }
            }

            // Insert into Transactions Table
            $stmt = $conn->prepare("
                INSERT INTO transactions
                (id, s_name, entry_type, customer, to_party, docket_no, docket_date, pincode, destination, weight, mode_of_tsp, s_pro, waybill_value, remarks, amount, entry_ts, waybill_percent, oda_chrg, owner_risk, carrier_risk, payment_status, payment_received_date, waybill, mobile1, mobile2, username, created_at)
                VALUES (NULL, '', 'credit', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Pending', NULL, NULL, NULL, NULL, ?, CURRENT_TIMESTAMP)
            ");

            // Get risk type and value from the row data
            $risk_type = $row[10] ?? 'No Risk'; // Assuming risk type is in column 11
            $risk_value = $row[11] ?? 0; // Assuming risk value is in column 12
            $oda_charges = $row[9] ?? 0; // Assuming ODA charges is in column 10

            // Set owner_risk and carrier_risk values based on risk type
            $owner_risk_value = ($risk_type === 'Owner Risk') ? $risk_value : 0;
            $carrier_risk_value = ($risk_type === 'Carrier Risk') ? $risk_value : 0;

            $stmt->bind_param("sssssssssss",
                $customer,
                $to_party,
                $docket_no,
                $docket_date,
                $pincode,
                $destination,
                $weight,
                $mode_of_tsp,
                $waybill_value,
                $remarks,
                $username,
                $amount,
                $entry_ts,
                $customer_rates['waybill_percent'],
                $oda_charges,
                $owner_risk_value,
                $carrier_risk_value
            );

            if (!$stmt->execute()) {
                $errorMessages[] = "Row $index: Database insertion failed.";
                $errorCount++;
            } else {
                $successMessages[] = "Row $index: Successfully processed.";
                $successCount++;
            }
            $stmt->close();
        }

        $_SESSION['successMessages'] = $successMessages;
        $_SESSION['errorMessages'] = $errorMessages;
        header('Location: error_page.php');
        exit();

    } catch (Exception $e) {
        error_log("Error in bulk upload processing: " . $e->getMessage());
        die("<script>alert('Error processing bulk upload: " . $e->getMessage() . "'); window.history.back();</script>");
    }
}

$conn->close();
error_log("=== Completed process_credit_entry.php execution ===");
?>
