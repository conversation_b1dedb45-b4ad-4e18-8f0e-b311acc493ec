<?php
session_start();
include 'db_connect.php';

$username = $_SESSION['username'];
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $id = $_POST['id'];
    $short_name = $_POST['short_name'];
    $mode_of_tsp = $_POST['mode_of_tsp'];
    $zone = $_POST['zone'];
    
    // Handle different field names based on mode
    if ($mode_of_tsp === 'Surface') {
        $up_to_0250 = $_POST['up_to_0250']; // 1-10 kg rate
        $up_to_0500 = $_POST['up_to_0500']; // 10-20 kg rate
        $addl_500gm = $_POST['addl_500gm']; // Above 20 kg rate
        $above_3kg = $_POST['min_weight']; // Minimum chargeable weight
    } else if ($mode_of_tsp === 'Premium') {
        // For Premium mode, set weight-based fields to 0 and use per kg rate
        $up_to_0250 = 0;
        $up_to_0500 = 0;
        $addl_500gm = 0;
        $above_3kg = $_POST['above_3kg_premium']; // Per kg rate
    } else {
        // For Express mode
        $up_to_0250 = $_POST['up_to_0250'];
        $up_to_0500 = $_POST['up_to_0500'];
        $addl_500gm = $_POST['addl_500gm'];
        $above_3kg = $_POST['above_3kg'];
    }

    // 🔥 Duplicate Validation Check
    $check_sql = "SELECT * FROM rate_master WHERE short_name = ? AND mode_of_tsp = ? AND ";
    if ($mode_of_tsp === 'Premium') {
        $check_sql .= "pr_zone = ? AND username = ?";
    } else {
        $check_sql .= "zone = ? AND username = ?";
    }
    
    if (!empty($id)) {
        $check_sql .= " AND id != ?";
    }

    $check_stmt = $conn->prepare($check_sql);
    if (!empty($id)) {
        if ($mode_of_tsp === 'Premium') {
            $check_stmt->bind_param("ssssi", $short_name, $mode_of_tsp, $zone, $username, $id);
        } else {
            $check_stmt->bind_param("ssssi", $short_name, $mode_of_tsp, $zone, $username, $id);
        }
    } else {
        if ($mode_of_tsp === 'Premium') {
            $check_stmt->bind_param("ssss", $short_name, $mode_of_tsp, $zone, $username);
        } else {
            $check_stmt->bind_param("ssss", $short_name, $mode_of_tsp, $zone, $username);
        }
    }
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows > 0) {
        $_SESSION['rate_message'] = "Duplicate combination not allowed!";
        header("Location: index.php?page=rate_master");
        exit;
    }

    if (empty($id)) {
        // Insert New Rate 🔥
        if ($mode_of_tsp === 'Premium') {
            $stmt = $conn->prepare("INSERT INTO rate_master (username, short_name, mode_of_tsp, pr_zone, up_to_0250, up_to_0500, addl_500gm, above_3kg, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("ssssdddd", $username, $short_name, $mode_of_tsp, $zone, $up_to_0250, $up_to_0500, $addl_500gm, $above_3kg);
        } else {
            $stmt = $conn->prepare("INSERT INTO rate_master (username, short_name, mode_of_tsp, zone, up_to_0250, up_to_0500, addl_500gm, above_3kg, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("ssssdddd", $username, $short_name, $mode_of_tsp, $zone, $up_to_0250, $up_to_0500, $addl_500gm, $above_3kg);
        }
    } else {
        // Update Existing Rate 🔥
        if ($mode_of_tsp === 'Premium') {
            $stmt = $conn->prepare("UPDATE rate_master SET short_name=?, mode_of_tsp=?, pr_zone=?, up_to_0250=?, up_to_0500=?, addl_500gm=?, above_3kg=? WHERE id=? AND username=?");
            $stmt->bind_param("ssssdddis", $short_name, $mode_of_tsp, $zone, $up_to_0250, $up_to_0500, $addl_500gm, $above_3kg, $id, $username);
        } else {
            $stmt = $conn->prepare("UPDATE rate_master SET short_name=?, mode_of_tsp=?, zone=?, up_to_0250=?, up_to_0500=?, addl_500gm=?, above_3kg=? WHERE id=? AND username=?");
            $stmt->bind_param("ssssdddis", $short_name, $mode_of_tsp, $zone, $up_to_0250, $up_to_0500, $addl_500gm, $above_3kg, $id, $username);
        }
    }

    if ($stmt->execute()) {
        $_SESSION['rate_message'] = empty($id) ? "Rate saved successfully!" : "Rate updated successfully!";
        header("Location: index.php?page=rate_master");
        exit;
    } else {
        $_SESSION['rate_message'] = "Failed to save rate!";
        header("Location: index.php?page=rate_master");
        exit;
    }
}
?>
